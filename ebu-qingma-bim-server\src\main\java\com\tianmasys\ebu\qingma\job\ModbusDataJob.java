package com.tianmasys.ebu.qingma.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.tianmasys.ebu.qingma.config.websocket.WebSocketService;
import com.tianmasys.ebu.qingma.dataprocess.modbus.storage.ModbusDataStorage;
import com.tianmasys.ebu.qingma.job.scheduler.TaskSchedulerManager;
import com.tianmasys.ebu.qingma.service.IModbusDataLogService;
import com.tianmasys.ebu.qingma.statistics.ModbusStatisticsProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Modbus数据定时任务
 * 定期从modbus设备读取数据，并通过WebSocket发送给客户端
 */
@Slf4j
@Component
public class ModbusDataJob {



    @Autowired
    private WebSocketService webSocketService;

    @Autowired
    private ModbusDataStorage modbusDataStorage;

    @Autowired
    private TaskSchedulerManager taskSchedulerManager;

    @Autowired
    private IModbusDataLogService modbusDataLogServiceImpl;

    @Autowired
    private ModbusStatisticsProcessor modbusStatisticsProcessor;

    /**
     * 在Spring Boot应用完全启动后启动定时任务
     */
    @PostConstruct
    public void startScheduledTask() {
        taskSchedulerManager.registerFixedRateTask("modbusDataJob", this::readModbusDataAndSend, 0,1000);
        log.info("Modbus数据定时任务已注册");
    }

    /**
     * 定时任务：定期读取modbus数据并发送到websocket
     * 每5秒执行一次
     */
    public void readModbusDataAndSend() {
        try {
            // 从共享存储中获取所有数据
            Map<String, Object> allData = modbusDataStorage.getAllData();
            if(!allData.isEmpty()){
                // 通过WebSocket发送数据
//                JSONObject jsonObject = dealWebSocketData(allData);
                JSONObject jsonObject = dealWebSocketData(allData);
//                log.info("{}", jsonObject);

                //数据保留
                modbusDataLogServiceImpl.asyncSaveModbusDataLog(jsonObject.getJSONObject("data"));

                //统计数据计算
                modbusStatisticsProcessor.processNewData(jsonObject.getJSONObject("data"));

                if(webSocketService.getSessionCount()> 0){
                    webSocketService.broadcastData(jsonObject);
                    log.debug("已通过WebSocket发送modbus数据");
                }
            }

        } catch (Exception e) {
            log.error("通过WebSocket发送modbus数据时发生错误", e);
        }
    }


    //websocket数据处理
    private JSONObject dealWebSocketData(Map<String, Object> allData ) {
        JSONObject result = new JSONObject();
        Map<String, JSONObject> groupedData = new ConcurrentHashMap<>();

        allData.forEach((key, value) -> {
            String[] keyParts = key.split("_");
            if (keyParts.length < 1) return;

            String groupKey = keyParts[0];
            JSONObject dataObj = JSONUtil.parseObj(value);

            if (!groupedData.containsKey(groupKey)) {
                JSONObject groupObj = new JSONObject();
                groupObj.set("data", new JSONObject());
                groupObj.set("startTime", dataObj.getLong("startTime"));
                groupObj.set("fnishTime", dataObj.getLong("fnishTime"));
                groupedData.put(groupKey, groupObj);
            }

            JSONObject groupObj = groupedData.get(groupKey);
            JSONObject data = dataObj.getJSONObject("data");

            // 合并data
            data.forEach((dataKey, dataValue) -> {
                groupObj.getJSONObject("data").set(dataKey, dataValue);
            });

            // 更新startTime和fnishTime
            long startTime = dataObj.getLong("startTime");
            long fnishTime = dataObj.getLong("fnishTime");
            if (startTime < groupObj.getLong("startTime")) {
                groupObj.set("startTime", startTime);
            }
            if (fnishTime > groupObj.getLong("fnishTime")) {
                groupObj.set("fnishTime", fnishTime);
            }

            // 更新其他字段
            groupObj.set("status", dataObj.get("status"));
            groupObj.set("slaveId", dataObj.get("slaveId"));
        });

        // 计算time并构建最终结果
        groupedData.forEach((key, value) -> {
            long startTime = value.getLong("startTime");
            long fnishTime = value.getLong("fnishTime");
            value.set("time", fnishTime - startTime);
            result.set(key, value);
        });

        JSONObject finalResult = new JSONObject();
        finalResult.set("data", result);
        finalResult.set("type", "realTimeData");
        return finalResult;

    }

}
