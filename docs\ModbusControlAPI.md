# Modbus设备控制API文档

## 概述
本文档描述了青马大桥BIM系统中Modbus设备控制相关的API接口，主要用于Vue前端控制面板的设备控制和状态监控。

## 接口列表

### 1. 获取控制面板状态
**接口地址：** `GET /modbusSite/controlPanel/{siteId}`

**功能描述：** 获取指定站点的设备控制面板状态信息

**请求参数：**
- `siteId` (Long): 站点ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "siteId": 1,
    "siteName": "青马大桥监控站点1",
    "operationMode": 0,
    "runningStatus": 1,
    "inverterStatus": 1,
    "inverterFaultStatus": 0,
    "bypassDamperStatus": 0,
    "airPretreatmentStatus": 1,
    "dehumidifierStatus": 0,
    "inletDamperStatus": 1,
    "outletDamperStatus": 1,
    "airPretreatmentFanStatus": 1,
    "airPretreatmentSpeed": 75,
    "temperatureSetting": 25,
    "humiditySetting": 60,
    "modeSetting": 1,
    "energyDemandSetting": 500
  }
}
```

### 2. 执行设备控制命令
**接口地址：** `POST /modbusSite/modbusCmd`

**功能描述：** 执行Modbus设备控制命令

**请求体示例：**
```json
{
  "pointId": 1,
  "value": 1,
  "cmdType": "WRITE",
  "description": "设置操作模式为自动"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "Modbus控制命令执行成功",
  "data": true
}
```

**请求参数说明：**
- `pointId` (Long): 点位ID，通过此ID可以查找到对应的站点、主站、从站、寄存器地址等信息
- `value` (Object): 写入值，支持数字和布尔值
- `cmdType` (String): 命令类型，READ-读取，WRITE-写入
- `description` (String): 命令描述，可选

## 点位配置说明

### 点位信息获取
通过`pointId`可以从`modbus_points`表中获取以下信息：
- `siteId`: 所属站点ID
- `masterId`: 主站ID
- `slaveId`: 从站ID
- `functionCode`: 功能码（1-COIL_STATUS, 2-INPUT_STATUS, 3-HOLDING_REGISTER, 4-INPUT_REGISTER）
- `registerAddress`: 寄存器地址
- `dataType`: 数据类型（2-TWO_BYTE_INT_UNSIGNED, 3-TWO_BYTE_INT_SIGNED）

### 功能码说明
| 功能码 | 类型 | 说明 | 支持操作 |
|--------|------|------|----------|
| 1 | COIL_STATUS | 线圈状态 | 读取、写入 |
| 2 | INPUT_STATUS | 输入状态 | 仅读取 |
| 3 | HOLDING_REGISTER | 保持寄存器 | 读取、写入 |
| 4 | INPUT_REGISTER | 输入寄存器 | 仅读取 |

### 数据类型说明
| 数据类型 | 说明 |
|----------|------|
| 2 | TWO_BYTE_INT_UNSIGNED（无符号16位整数） |
| 3 | TWO_BYTE_INT_SIGNED（有符号16位整数） |

## Vue前端使用示例

### 1. 获取控制面板状态
```javascript
// 获取控制面板状态
async getControlPanelStatus(siteId) {
  try {
    const response = await this.$http.get(`/modbusSite/controlPanel/${siteId}`);
    if (response.code === 200) {
      this.controlPanel = response.data;
    }
  } catch (error) {
    this.$message.error('获取控制面板状态失败');
  }
}
```

### 2. 设备控制操作
```javascript
// 通用设备控制方法
async executeModbusCmd(pointId, value, description) {
  const cmdData = {
    pointId: pointId,
    value: value,
    cmdType: "WRITE",
    description: description
  };

  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('控制命令执行成功');
      // 刷新控制面板状态
      this.getControlPanelStatus(this.siteId);
    }
  } catch (error) {
    this.$message.error('控制命令执行失败');
  }
}

// 读取设备状态
async readModbusPoint(pointId, description) {
  const cmdData = {
    pointId: pointId,
    value: 0, // 读取操作时值可以为任意
    cmdType: "READ",
    description: description
  };

  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('读取成功');
    }
  } catch (error) {
    this.$message.error('读取失败');
  }
}

// 使用示例
// 切换操作模式（假设pointId=1对应操作模式点位）
this.executeModbusCmd(1, 1, "切换到自动模式");

// 启动变频器（假设pointId=2对应变频器启停点位）
this.executeModbusCmd(2, 1, "启动变频器");

// 设置温度（假设pointId=3对应温度设定点位）
this.executeModbusCmd(3, 25, "设置温度为25℃");

// 读取设备状态（假设pointId=4对应运行状态点位）
this.readModbusPoint(4, "读取运行状态");
```

## 权限要求
- 查询控制面板状态：`qingma:modbusSite:query`
- 执行控制命令：`qingma:modbusSite:cmd`

## 注意事项
1. 手动模式下才能进行手动控制操作
2. 所有控制操作都会记录操作日志
3. 建议在执行控制命令后刷新控制面板状态
4. 异常情况下会返回相应的错误信息
5. 控制命令执行具有实时性，请确保网络连接稳定
