package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

/**
 * Modbus点位值VO
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "Modbus点位值")
public class ModbusPointValueVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "点位ID")
    private Long pointId;

    @Schema(description = "点位名称")
    private String pointName;

    @Schema(description = "点位描述")
    private String description;

    @Schema(description = "点位值")
    private Object value;

    @Schema(description = "功能码")
    private String functionCode;

    @Schema(description = "寄存器地址")
    private Integer registerAddress;

    @Schema(description = "读取状态：true-成功，false-失败")
    private Boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;
}
