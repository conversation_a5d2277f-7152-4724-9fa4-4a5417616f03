package com.tianmasys.ebu.qingma.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusDataLogMapper;
import com.tianmasys.ebu.qingma.service.IModbusDataLogService;
import com.tianmasys.ebu.qingma.vo.ModbusDataLogVO;
import com.tianmasys.ebu.qingma.domain.ModbusDataLogEntity;

/**
 * modbus点位数据记录表Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Service
@Slf4j
public class ModbusDataLogServiceImpl extends ServiceImpl<ModbusDataLogMapper,ModbusDataLogEntity> implements IModbusDataLogService 
{
    @Autowired
    private ModbusDataLogMapper modbusDataLogMapper;

    /**
     * 查询modbus点位数据记录表详情
     * 
     * @param id modbus点位数据记录表主键
     * @return modbus点位数据记录表
     */
    @Override
    public ModbusDataLogVO selectModbusDataLogById(Long id)
    {
        ModbusDataLogEntity entity = modbusDataLogMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusDataLogVO.class);
    }

    /**
     * 查询modbus点位数据记录表列表
     * 
     * @param modbusDataLogVO modbus点位数据记录表
     * @return modbus点位数据记录表
     */
    @Override
    public List<ModbusDataLogVO> selectModbusDataLogList(ModbusDataLogVO modbusDataLogVO)
    {
        List<ModbusDataLogEntity> modbusDataLogEntities = modbusDataLogMapper.selectList(getWrapper(modbusDataLogVO));
        return PageObjectConvertUtil.convert(modbusDataLogEntities,ModbusDataLogVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusDataLogEntity> getWrapper(ModbusDataLogVO query){
        LambdaQueryWrapper<ModbusDataLogEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusDataLogEntity::getSiteId, query.getSiteId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getSlaveId()), ModbusDataLogEntity::getSlaveId, query.getSlaveId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStartTime()), ModbusDataLogEntity::getStartTime, query.getStartTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getFnishTime()), ModbusDataLogEntity::getFnishTime, query.getFnishTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getTime()), ModbusDataLogEntity::getTime, query.getTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getData()), ModbusDataLogEntity::getData, query.getData());
        return wrapper;
    }

    /**
     * 新增modbus点位数据记录表
     * 
     * @param modbusDataLogVO modbus点位数据记录表
     * @return 结果
     */
    @Override
    public int insertModbusDataLog(ModbusDataLogVO modbusDataLogVO)
    {
       ModbusDataLogEntity entity = BeanUtil.toBean(modbusDataLogVO, ModbusDataLogEntity.class);
       return modbusDataLogMapper.insert(entity);
    }

    /**
     * 修改modbus点位数据记录表
     * 
     * @param modbusDataLogVO modbus点位数据记录表
     * @return 结果
     */
    @Override
    public int updateModbusDataLog(ModbusDataLogVO modbusDataLogVO)
    {
       ModbusDataLogEntity entity = BeanUtil.toBean(modbusDataLogVO, ModbusDataLogEntity.class);
        return modbusDataLogMapper.updateById(entity);
    }

    /**
     * 批量删除modbus点位数据记录表
     * 
     * @param ids 需要删除的modbus点位数据记录表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataLogByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusDataLogMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus点位数据记录表信息
     * 
     * @param id modbus点位数据记录表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataLogById(Long id)
    {

        return modbusDataLogMapper.deleteById(id);
    }

    /**
     * 异步-批量保存modbusDataLog
     *
     * @param jsonObject
     * @return
     */
    @Override
    @Async
    public Integer asyncSaveModbusDataLog(JSONObject jsonObject) {
        try {
            if (jsonObject == null || jsonObject.isEmpty()) {
                log.warn("传入的数据为空，跳过保存");
                return 0;
            }

            List<ModbusDataLogEntity> modbusDataLogEntityList = jsonObject.keySet().stream().map(key -> {
                try {
                    JSONObject jsonData = jsonObject.getJSONObject(key);
                    if (jsonData != null) {
                        jsonData.put("siteId", key);
                        return JSONUtil.toBean(jsonData, ModbusDataLogEntity.class);
                    }
                    return null;
                } catch (Exception e) {
                    log.error("解析数据项 {} 时发生错误", key, e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (!modbusDataLogEntityList.isEmpty()) {
                modbusDataLogMapper.insertBatch(modbusDataLogEntityList);
//                log.info("成功保存 {} 条Modbus数据日志", modbusDataLogEntityList.size());
            } else {
                log.warn("没有有效的数据需要保存");
            }

            return modbusDataLogEntityList.size();
        } catch (Exception e) {
            log.error("异步保存Modbus数据日志时发生错误", e);
            return 0; // 发生异常时返回0而不是null
        }
    }

}
