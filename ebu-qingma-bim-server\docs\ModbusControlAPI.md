# Modbus设备控制API文档

## 概述
本文档描述了青马大桥BIM系统中Modbus设备控制相关的API接口，主要用于Vue前端控制面板的设备控制和状态监控。

## 接口列表

### 1. 获取控制面板状态
**接口地址：** `GET /modbusSite/controlPanel/{siteId}`

**功能描述：** 获取指定站点的设备控制面板状态信息

**请求参数：**
- `siteId` (Long): 站点ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "siteId": 1,
    "siteName": "青马大桥监控站点1",
    "operationMode": 0,
    "runningStatus": 1,
    "inverterStatus": 1,
    "inverterFaultStatus": 0,
    "bypassDamperStatus": 0,
    "airPretreatmentStatus": 1,
    "dehumidifierStatus": 0,
    "inletDamperStatus": 1,
    "outletDamperStatus": 1,
    "airPretreatmentFanStatus": 1,
    "airPretreatmentSpeed": 75,
    "temperatureSetting": 25,
    "humiditySetting": 60,
    "modeSetting": 1,
    "energyDemandSetting": 500
  }
}
```

### 2. 执行设备控制命令
**接口地址：** `POST /modbusSite/modbusCmd`

**功能描述：** 执行Modbus设备控制命令

**请求体示例：**
```json
{
  "siteId": 1,
  "masterId": 1,
  "slaveId": 1,
  "registerAddress": 1001,
  "value": 1,
  "dataType": "HOLDING_REGISTER",
  "cmdType": "WRITE",
  "description": "设置操作模式为自动"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "Modbus控制命令执行成功",
  "data": true
}
```

## 控制地址映射

### 状态显示区域
| 功能 | 地址 | 说明 |
|------|------|------|
| 操作模式 | 1001 | 0-手动，1-自动 |
| 运行状态 | 1002 | 0-停止，1-运行 |
| 变频器启停 | 1003 | 0-停止，1-启动 |
| 变频器故障复位 | 1004 | 0-正常，1-故障 |

### 手动模式控制区域
| 功能 | 地址 | 说明 |
|------|------|------|
| 旁路风阀开启关闭 | 1010 | 0-关闭，1-开启 |
| 空气预处理器启停 | 1011 | 0-停止，1-启动 |
| 除湿机启停 | 1012 | 0-停止，1-启动 |
| 进风阀开启关闭 | 1013 | 0-关闭，1-开启 |
| 出风口风阀开启关闭 | 1014 | 0-关闭，1-开启 |
| 空气预处理器风机启停 | 1015 | 0-停止，1-启动 |
| 空气预处理器风速调节 | 1016 | 0-100 |

### 参数设定区域
| 功能 | 地址 | 说明 |
|------|------|------|
| 温度设定 | 1020 | 温度值（摄氏度） |
| 湿度设定 | 1021 | 湿度值（百分比） |
| 模式设定 | 1022 | 1-制冷，2-制热，3-热源制热，4-通风，6-自动 |
| 开放能需设定 | 1023 | 0-1000 |

## Vue前端使用示例

### 1. 获取控制面板状态
```javascript
// 获取控制面板状态
async getControlPanelStatus(siteId) {
  try {
    const response = await this.$http.get(`/modbusSite/controlPanel/${siteId}`);
    if (response.code === 200) {
      this.controlPanel = response.data;
    }
  } catch (error) {
    this.$message.error('获取控制面板状态失败');
  }
}
```

### 2. 设备控制操作
```javascript
// 切换操作模式
async switchOperationMode(siteId, masterId, slaveId, mode) {
  const cmdData = {
    siteId: siteId,
    masterId: masterId,
    slaveId: slaveId,
    registerAddress: 1001,
    value: mode, // 0-手动，1-自动
    dataType: "HOLDING_REGISTER",
    cmdType: "WRITE",
    description: mode === 0 ? "切换到手动模式" : "切换到自动模式"
  };
  
  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('操作模式切换成功');
      // 刷新控制面板状态
      this.getControlPanelStatus(siteId);
    }
  } catch (error) {
    this.$message.error('操作模式切换失败');
  }
}

// 变频器启停控制
async controlInverter(siteId, masterId, slaveId, action) {
  const cmdData = {
    siteId: siteId,
    masterId: masterId,
    slaveId: slaveId,
    registerAddress: 1003,
    value: action, // 0-停止，1-启动
    dataType: "HOLDING_REGISTER",
    cmdType: "WRITE",
    description: action === 0 ? "停止变频器" : "启动变频器"
  };
  
  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('变频器控制成功');
      this.getControlPanelStatus(siteId);
    }
  } catch (error) {
    this.$message.error('变频器控制失败');
  }
}

// 设置温度
async setTemperature(siteId, masterId, slaveId, temperature) {
  const cmdData = {
    siteId: siteId,
    masterId: masterId,
    slaveId: slaveId,
    registerAddress: 1020,
    value: temperature,
    dataType: "HOLDING_REGISTER",
    cmdType: "WRITE",
    description: `设置温度为${temperature}℃`
  };
  
  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('温度设置成功');
      this.getControlPanelStatus(siteId);
    }
  } catch (error) {
    this.$message.error('温度设置失败');
  }
}
```

## 权限要求
- 查询控制面板状态：`qingma:modbusSite:query`
- 执行控制命令：`qingma:modbusSite:cmd`

## 注意事项
1. 手动模式下才能进行手动控制操作
2. 所有控制操作都会记录操作日志
3. 建议在执行控制命令后刷新控制面板状态
4. 异常情况下会返回相应的错误信息
5. 控制命令执行具有实时性，请确保网络连接稳定
