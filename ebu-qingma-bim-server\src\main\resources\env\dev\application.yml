# Tomcat
server:
   port: 18001
   servlet:
      context-path: /

# spring配置
spring:
   main:
      # 循环引用
      allow-circular-references: true
   application:
      # 应用名称
      name: ebu-qingma-bim-server
   redis:
      #host: **************
      #port: 26379
      host: ************
      port: 6379
      password:
      database: 3
   datasource:
      druid:
         stat-view-servlet:
            enabled: true
            loginUsername: admin
            loginPassword: 123456
      dynamic:
         druid:
            initial-size: 5
            min-idle: 5
            maxActive: 20
            maxWait: 60000
            timeBetweenEvictionRunsMillis: 60000
            minEvictableIdleTimeMillis: 300000
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            poolPreparedStatements: true
            maxPoolPreparedStatementPerConnectionSize: 20
            filters: stat,slf4j
            connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
         datasource:
            # 主库数据源
            master:
               driver-class-name: com.mysql.cj.jdbc.Driver
#               url: ******************************************************************************************************************************************************
#               url: ***********************************************************************************************************************************************************
               url: ***********************************************************************************************************************************************************
               username: root
               password: Dl123456
               # 从库数据源
               # slave:
               # username:
               # password:
               # url:
               # driver-class-name:

## mybatis配置
#mybatis:
#   # 搜索指定包别名
#   typeAliasesPackage: com.tianmasys.ebu.system
#   # 配置mapper的扫描，找到所有的mapper.xml映射文件
#   mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
   title: 系统模块接口文档
   license: Powered By ruoyi
   licenseUrl: https://ruoyi.vip


# 安全配置
security:
   # 验证码
   captcha:
      enabled: true
      type: math
   # 防止XSS攻击
   xss:
      enabled: true
      excludeUrls:
         - /system/notice
   # 不校验白名单
   ignore:
      whites:
         - /auth/logout
         - /auth/login
         - /auth/register
         - /*/v2/api-docs
         - /csrf
         - /auth/cust/*
         - /code
         - /wxmp/**
         - /iot/device/dealMqttEvent
         - /qingma/**

# rabbitmq相关配置
mq:
   biz:
      #默认交换机
      exchange: default-exchange-joke
      #死信队列
      dead-letter: {exchange: dlx_exchange_joke,routingKey: dlx_queue_joke,queue: dlx_queue_joke}
      #队列信息
      queueMap:
         # 客户浏览
         custBrowse: {queue: queue-joke-custBrowse_dev , routingKey: rk_joke_custBrowse_dev, exchange: default-exchange-joke }
         # 客户动作
         custAction: {queue: queue-joke-custAction_dev , routingKey: rk_joke_custAction_dev, exchange: default-exchange-joke }
      #消费者配置
      openFlag:
         CustBrowseConsumer : {open : 1, concurrency: 1}

mybatis-plus:
   mapper-locations: classpath*:mapper/**/*.xml
   typeAliasesPackage: com.tianmasys.ebu.*.domain,com.tianmasys.ebu.module.*.domain,com.tianmasys.ebu.system.api.domain
   global-config:
      db-config:
         id-type: AUTO
      banner: false
   configuration:
      map-underscore-to-camel-case: true
      cache-enabled: false
      call-setters-on-nulls: true
      jdbc-type-for-null: 'null'
   configuration-properties:
      prefix:
      blobType: BLOB
      boolValue: TRUE

biz:
   logging: true
   # 映射的路由
   routes:
      # 认证中心
      - id: ebu-auth
        uri: /
        pathPrefix: /auth/**
        prefixToRemove : /auth/
      # 系统模块
      - id: ebu-system
        uri: /
        pathPrefix: /system/**
        prefixToRemove: /system/
      # iot
      - id: ebu-iot
        uri: /
        pathPrefix: /iot/**
        prefixToRemove: /iot/
      # file
      - id: ebu-file
        uri: /
        pathPrefix: /file/**
        prefixToRemove: /file/
      # qingma
      - id: ebu-qingma
        uri: /
        pathPrefix: /qingma/**
        prefixToRemove: /qingma/
   # feign映射配置
   feignRoutes:
      # 系统模块
      ebu-system: http://127.0.0.1:18001
      # portal模块
      ebu-portal: http://127.0.0.1:18001
      # file模块
      ebu-file: http://127.0.0.1:18001

#iot:
#   emq:
#      # 账号
#      username: user_zawl
#      # 密码
#      password: zawl1234
#      # 主机地址
#      hostUrl: tcp://************:1883
##      hostUrl: tcp://mqtt.tianmasys.com:19002
#      # 客户端Id，不能相同，采用随机数 ${random.value}
#      client-id: ${random.int(100000000,900000000)}
#      # 默认主题
##      default-topic: device_msg
#      default-topic: /pub/5qmeux233vfou9nt
#      # 保持连接
#      keepalive: 60
#      # 清除会话(设置为false,断开连接，重连后使用原来的会话 保留订阅的主题，能接收离线期间的消息)
#      clearSession: true
#      # rest api 秘钥
#      http:
#         key: 683f7c9685d6ddd2
#         secret: Db9B6jFjBc3LpslwcCmswlH2ggVyuLQl7gpdqCX9CZP5D
#         url: http://************:18083/api/v5

#   modbus:
#      masters:
#      - id: 1
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 1
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 2
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 2
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 3
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 3
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 4
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 4
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 5
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 1
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 6
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 2
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 7
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 3
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 8
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 4
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 9
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 1
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 10
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 2
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 11
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 3
#           offset: 9,10,11,12
#           autoRead: true
#           interval: 1000
#      - id: 12
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 4
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000
#      - id: 13
#        host: ************
#        port: 502
#        keepAlive: true
#        slaves:
#         - slaveId: 4
#           offset: 99,100,101,102
#           autoRead: true
#           interval: 1000

#开发环境
system:
   #是否为开发环境,不校验权限
   checkAuth: false
#   checkAuth: true

## 开启代理
#proxy:
#   http:
#      enable: true
#      # 代理地址 对应公网地址为**************
#      host: ************
#      port: 13128

#ebu:
#   system:
#      url: http://127.0.0.1:18001

feign:
   httpclient:
      enabled: false
   okhttp:
      enabled: true

## 本地文件上传
#file:
#   domain: http://127.0.0.1:8004/ebu-file
#   path: /home/<USER>/files/upload
#   prefix: /statics

## FastDFS配置
#fdfs:
#   domain: http://************
#   soTimeout: 3000
#   connectTimeout: 2000
#   trackerList: ************:22122
#
## Minio配置
#minio:
#   url: http://************:9000
#   accessKey: minioadmin
#   secretKey: minioadmin
#   bucketName: test
#
#
   # 阿里云oss配置
aliyunoss:
   bucket: ebu-cloud-imageoss
   accessKeyId: LTAI5tGH4Fbtzb2MXmqUEBWs
   accessKeySecret: ******************************
   #外网访问
   address: oss-cn-shanghai.aliyuncs.com
   hostDomain: https://imageoss.wanxiangpan.com
   isUse: true

## http服务上传
#httpFile:
#   url: http://127.0.0.1:8004/ebu-file/fileInfo/uploadFile
