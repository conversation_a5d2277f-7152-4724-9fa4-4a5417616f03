package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ModbusDataStatisticsVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataRequestVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataResponseVO;
import com.tianmasys.ebu.qingma.service.IModbusDataStatisticsService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * modbus统计数据统计表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-08-07
 */
@RestController
@RequestMapping("/modbusDataStatistics")
public class ModbusDataStatisticsController extends BaseController
{
    @Autowired
    private IModbusDataStatisticsService modbusDataStatisticsService;

    /**
     * 查询modbus统计数据统计表列表
     */
    @RequiresPermissions("qingma:modbusDataStatistics:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ModbusDataStatisticsVO>> list(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        startPage();
        List<ModbusDataStatisticsVO> list = modbusDataStatisticsService.selectModbusDataStatisticsList(modbusDataStatisticsVO);
        return success(getDataTable(list));
    }


    /**
     * 导出modbus统计数据统计表列表
     */
    @RequiresPermissions("qingma:modbusDataStatistics:export")
    @Log(title = "modbus统计数据统计表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        List<ModbusDataStatisticsVO> list = modbusDataStatisticsService.selectModbusDataStatisticsList(modbusDataStatisticsVO);
        ExcelUtil<ModbusDataStatisticsVO> util = new ExcelUtil<ModbusDataStatisticsVO>(ModbusDataStatisticsVO.class);
        util.exportExcel(response, list, "modbus统计数据统计表数据");
    }

    /**
     * 获取modbus统计数据统计表详细信息
     */
    @RequiresPermissions("qingma:modbusDataStatistics:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ModbusDataStatisticsVO> getInfo(@PathVariable("id") Long id)
    {
        return success(modbusDataStatisticsService.selectModbusDataStatisticsById(id));
    }

    /**
     * 新增modbus统计数据统计表
     */
    @RequiresPermissions("qingma:modbusDataStatistics:add")
    @Log(title = "modbus统计数据统计表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ModbusDataStatisticsVO> add(@Valid @RequestBody ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        return toAjax(modbusDataStatisticsService.insertModbusDataStatistics(modbusDataStatisticsVO));
    }

    /**
     * 修改modbus统计数据统计表
     */
    @RequiresPermissions("qingma:modbusDataStatistics:edit")
    @Log(title = "modbus统计数据统计表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ModbusDataStatisticsVO> edit(@Valid @RequestBody ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        return toAjax(modbusDataStatisticsService.updateModbusDataStatistics(modbusDataStatisticsVO));
    }

    /**
     * 删除modbus统计数据统计表
     */
    @RequiresPermissions("qingma:modbusDataStatistics:remove")
    @Log(title = "modbus统计数据统计表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ModbusDataStatisticsVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(modbusDataStatisticsService.deleteModbusDataStatisticsByIds(ids));
    }

    /**
     * 获取历史统计数据
     */
//    @RequiresPermissions("qingma:modbusDataStatistics:query")
//    @Log(title = "获取历史统计数据", businessType = BusinessType.OTHER)
    @PostMapping("/statistics/getHistoryData")
    public AjaxResult<HistoryDataResponseVO> getHistoryData(@Valid @RequestBody HistoryDataRequestVO request)
    {
        HistoryDataResponseVO response = modbusDataStatisticsService.getHistoryData(request);
        return success(response);
    }
}
