package com.tianmasys.ebu.qingma.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusDataStatisticsMapper;
import com.tianmasys.ebu.qingma.service.IModbusDataStatisticsService;
import com.tianmasys.ebu.qingma.vo.ModbusDataStatisticsVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataRequestVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataResponseVO;
import com.tianmasys.ebu.qingma.domain.ModbusDataStatisticsEntity;
import com.tianmasys.ebu.qingma.statistics.ModbusStatisticsProcessor;
import com.tianmasys.ebu.qingma.statistics.ModbusStatisticsData;
import lombok.extern.slf4j.Slf4j;

/**
 * modbus统计数据统计表Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-07
 */

@Slf4j
@Service
public class ModbusDataStatisticsServiceImpl extends ServiceImpl<ModbusDataStatisticsMapper,ModbusDataStatisticsEntity> implements IModbusDataStatisticsService
{
    @Autowired
    private ModbusDataStatisticsMapper modbusDataStatisticsMapper;

    @Autowired
    private ModbusStatisticsProcessor modbusStatisticsProcessor;

    /**
     * 查询modbus统计数据统计表详情
     * 
     * @param id modbus统计数据统计表主键
     * @return modbus统计数据统计表
     */
    @Override
    public ModbusDataStatisticsVO selectModbusDataStatisticsById(Long id)
    {
        ModbusDataStatisticsEntity entity = modbusDataStatisticsMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusDataStatisticsVO.class);
    }

    /**
     * 查询modbus统计数据统计表列表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return modbus统计数据统计表
     */
    @Override
    public List<ModbusDataStatisticsVO> selectModbusDataStatisticsList(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        List<ModbusDataStatisticsEntity> modbusDataStatisticsEntities = modbusDataStatisticsMapper.selectList(getWrapper(modbusDataStatisticsVO));
        return PageObjectConvertUtil.convert(modbusDataStatisticsEntities,ModbusDataStatisticsVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusDataStatisticsEntity> getWrapper(ModbusDataStatisticsVO query){
        LambdaQueryWrapper<ModbusDataStatisticsEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusDataStatisticsEntity::getSiteId, query.getSiteId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStatType()), ModbusDataStatisticsEntity::getStatType, query.getStatType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStartTime()), ModbusDataStatisticsEntity::getStartTime, query.getStartTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEndTime()), ModbusDataStatisticsEntity::getEndTime, query.getEndTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getData()), ModbusDataStatisticsEntity::getData, query.getData());
        return wrapper;
    }

    /**
     * 新增modbus统计数据统计表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return 结果
     */
    @Override
    public int insertModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
       ModbusDataStatisticsEntity entity = BeanUtil.toBean(modbusDataStatisticsVO, ModbusDataStatisticsEntity.class);
       return modbusDataStatisticsMapper.insert(entity);
    }

    /**
     * 修改modbus统计数据统计表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return 结果
     */
    @Override
    public int updateModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
       ModbusDataStatisticsEntity entity = BeanUtil.toBean(modbusDataStatisticsVO, ModbusDataStatisticsEntity.class);
        return modbusDataStatisticsMapper.updateById(entity);
    }

    /**
     * 批量删除modbus统计数据统计表
     * 
     * @param ids 需要删除的modbus统计数据统计表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataStatisticsByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusDataStatisticsMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus统计数据统计表信息
     *
     * @param id modbus统计数据统计表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataStatisticsById(Long id)
    {

        return modbusDataStatisticsMapper.deleteById(id);
    }

    /**
     * 获取历史统计数据
     *
     * @param request 历史数据查询请求参数
     * @return 历史数据响应结果
     */
    @Override
    public HistoryDataResponseVO getHistoryData(HistoryDataRequestVO request) {
        log.info("开始获取历史数据，站点ID: {}, 数据类型: {}, 时间周期: {}",
                request.getSiteId(), request.getDataType(), request.getPeriod());

        HistoryDataResponseVO response = new HistoryDataResponseVO();
        response.setSiteId(request.getSiteId());
        response.setDataType(request.getDataType());
        response.setPeriod(request.getPeriod());

        // 计算查询时间范围
        Date endTime = new Date();
        Date startTime = calculateStartTime(request.getPeriod(), endTime);
        response.setStartTime(startTime);
        response.setEndTime(endTime);

        List<HistoryDataResponseVO.HistoryDataItem> dataList = new ArrayList<>();

        // 根据period确定需要查询的统计类型和数据范围
        String[] statTypes = determineStatTypes(request.getPeriod());

        for (String statType : statTypes) {
            // 从数据库查询历史统计数据
            List<HistoryDataResponseVO.HistoryDataItem> dbData =
                    queryDatabaseData(request.getSiteId(), request.getDataType(), statType, startTime, endTime);
            dataList.addAll(dbData);
        }

        // 获取内存中的当前分钟数据（如果需要）
        if (shouldIncludeMemoryData(request.getPeriod())) {
            HistoryDataResponseVO.HistoryDataItem memoryData =
                    getMemoryData(request.getSiteId(), request.getDataType());
            if (memoryData != null) {
                dataList.add(memoryData);
            }
        }

        // 按时间排序
        dataList.sort(Comparator.comparing(HistoryDataResponseVO.HistoryDataItem::getTime));
        response.setDataList(dataList);

        log.info("获取历史数据完成，共{}条记录", dataList.size());
        return response;
    }

    /**
     * 根据时间周期计算开始时间
     */
    private Date calculateStartTime(String period, Date endTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(endTime);

        switch (period) {
            case "1h":
                cal.add(Calendar.HOUR_OF_DAY, -1);
                break;
            case "6h":
                cal.add(Calendar.HOUR_OF_DAY, -6);
                break;
            case "12h":
                cal.add(Calendar.HOUR_OF_DAY, -12);
                break;
            case "24h":
                cal.add(Calendar.HOUR_OF_DAY, -24);
                break;
            case "7d":
                cal.add(Calendar.DAY_OF_MONTH, -7);
                break;
            default:
                cal.add(Calendar.HOUR_OF_DAY, -1);
        }

        return cal.getTime();
    }

    /**
     * 根据时间周期确定需要查询的统计类型
     */
    private String[] determineStatTypes(String period) {
        switch (period) {
            case "1h":
                return new String[]{"minute"};
            case "6h":
            case "12h":
            case "24h":
                return new String[]{"hour"};
            case "7d":
                return new String[]{"day"};
            default:
                return new String[]{"minute"};
        }
    }

    /**
     * 判断是否需要包含内存数据
     */
    private boolean shouldIncludeMemoryData(String period) {
        // 对于1小时的查询，需要包含当前分钟的内存数据
        return "1h".equals(period);
    }

    /**
     * 从数据库查询历史统计数据
     */
    @SuppressWarnings("unchecked")
    private List<HistoryDataResponseVO.HistoryDataItem> queryDatabaseData(
            Long siteId, String dataType, String statType, Date startTime, Date endTime) {

        List<HistoryDataResponseVO.HistoryDataItem> result = new ArrayList<>();

        try {
            // 构建查询条件
            LambdaQueryWrapper<ModbusDataStatisticsEntity> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ModbusDataStatisticsEntity::getSiteId, siteId)
                   .eq(ModbusDataStatisticsEntity::getStatType, statType)
                   .ge(ModbusDataStatisticsEntity::getStartTime, startTime)
                   .le(ModbusDataStatisticsEntity::getEndTime, endTime)
                   .orderByAsc(ModbusDataStatisticsEntity::getStartTime);

            List<ModbusDataStatisticsEntity> entities = modbusDataStatisticsMapper.selectList(wrapper);

            for (ModbusDataStatisticsEntity entity : entities) {
                if (entity.getData() != null) {
                    Map<String, Object> dataMap =  entity.getData();
                    Object dataTypeObj = dataMap.get(dataType);

                    if (dataTypeObj instanceof Map) {
                        Map<String, Object> dataTypeMap = (Map<String, Object>) dataTypeObj;

                        HistoryDataResponseVO.HistoryDataItem item = new HistoryDataResponseVO.HistoryDataItem();
                        item.setTime(entity.getStartTime());
                        item.setAvgValue(getDoubleValue(dataTypeMap.get("avg")));
                        item.setLastValue(dataTypeMap.get("last"));
                        item.setCount(getIntegerValue(dataTypeMap.get("count")));
                        item.setSource("database");
                        item.setUnit(dataTypeMap.get("unit"));
                        item.setStatus(dataTypeMap.get("status"));
                        item.setTrend(dataTypeMap.get("trend"));

                        result.add(item);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询数据库历史数据时出错: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从内存获取当前分钟数据
     */
    private HistoryDataResponseVO.HistoryDataItem getMemoryData(Long siteId, String dataType) {
        try {
            ModbusStatisticsData statisticsData = modbusStatisticsProcessor.getStatisticsData();
            ModbusStatisticsData.SiteStatisticsData siteData = statisticsData.getSiteDataMap().get(siteId);

            if (siteData != null) {
                ModbusStatisticsData.StatisticTypeData minuteData = siteData.getTypeDataMap().get("minute");
                if (minuteData != null) {
                    ModbusStatisticsData.DataPointStats dataPointStats = minuteData.getDataPoints().get(dataType);
                    if (dataPointStats != null && dataPointStats.getCount() > 0) {
                        HistoryDataResponseVO.HistoryDataItem item = new HistoryDataResponseVO.HistoryDataItem();
                        item.setTime(new Date());
                        item.setAvgValue(dataPointStats.getAverage());
                        item.setLastValue(dataPointStats.getLastValue());
                        item.setCount(dataPointStats.getCount());
                        item.setSource("memory");
                        item.setUnit(dataPointStats.getUnit());
                        item.setStatus(dataPointStats.getStatus());
                        item.setTrend(dataPointStats.getTrend());

                        return item;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取内存数据时出错: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 安全地获取Double值
     */
    private Double getDoubleValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全地获取Integer值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
