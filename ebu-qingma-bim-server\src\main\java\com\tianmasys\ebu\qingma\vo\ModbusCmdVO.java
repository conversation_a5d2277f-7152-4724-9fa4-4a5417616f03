package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Modbus设备控制命令VO
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "Modbus设备控制命令")
public class ModbusCmdVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "点位ID")
    @NotNull(message = "点位ID不能为空")
    private Long pointId;

    @Schema(description = "写入值")
    @NotNull(message = "写入值不能为空")
    private Object value;

    @Schema(description = "命令类型：READ-读取，WRITE-写入")
    @NotNull(message = "命令类型不能为空")
    private String cmdType;

    @Schema(description = "命令描述")
    private String description;

    // 预定义的控制命令常量
    public static class CmdType {
        public static final String READ = "READ";
        public static final String WRITE = "WRITE";
    }

    // 预定义的设备控制地址常量
    public static class ControlAddress {
        // 操作模式相关
        public static final int OPERATION_MODE = 1001;           // 操作模式：0-手动，1-自动
        public static final int RUNNING_STATUS = 1002;          // 运行状态
        public static final int INVERTER_START_STOP = 1003;     // 变频器启停
        public static final int INVERTER_FAULT_RESET = 1004;    // 变频器故障复位

        // 手动模式控制
        public static final int BYPASS_DAMPER = 1010;           // 旁路风阀开启关闭
        public static final int AIR_PRETREATMENT_START = 1011;  // 空气预处理器启停
        public static final int DEHUMIDIFIER_START = 1012;      // 除湿机启停
        public static final int INLET_DAMPER = 1013;            // 进风阀开启关闭
        public static final int OUTLET_DAMPER = 1014;           // 出风口风阀开启关闭
        public static final int AIR_PRETREATMENT_FAN = 1015;    // 空气预处理器风机启停
        public static final int AIR_PRETREATMENT_SPEED = 1016;  // 空气预处理器风速调节

        // 参数设定
        public static final int TEMPERATURE_SETTING = 1020;     // 温度设定
        public static final int HUMIDITY_SETTING = 1021;        // 湿度设定
        public static final int MODE_SETTING = 1022;            // 模式设定
        public static final int ENERGY_DEMAND_SETTING = 1023;   // 开放能需设定
    }

    // 预定义的模式设定值
    public static class ModeValue {
        public static final int COOLING = 1;        // 制冷运行
        public static final int HEATING = 2;        // 制热运行
        public static final int HEAT_SOURCE = 3;    // 热源制热运行
        public static final int VENTILATION = 4;    // 通风运行
        public static final int AUTO = 6;           // 自动
    }
}
