package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tianmasys.ebu.qingma.domain.ModbusConfigData;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;
import com.tianmasys.ebu.qingma.mapper.ModbusPointsMapper;
import com.tianmasys.ebu.qingma.service.IModbusPointsService;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import com.tianmasys.ebu.qingma.vo.ModbusCmdVO;
import com.tianmasys.ebu.qingma.vo.ModbusControlPanelVO;
import com.tianmasys.ebu.qingma.vo.ModbusPointValueVO;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.ModbusConfig;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.utils.Modbus4jUtils;
import com.serotonin.modbus4j.code.DataType;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusSiteMapper;
import com.tianmasys.ebu.qingma.service.IModbusSiteService;
import com.tianmasys.ebu.qingma.vo.ModbusSiteVO;
import com.tianmasys.ebu.qingma.domain.ModbusSiteEntity;

/**
 * modbus站点Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Slf4j
@Service
public class ModbusSiteServiceImpl extends ServiceImpl<ModbusSiteMapper,ModbusSiteEntity> implements IModbusSiteService
{
    @Autowired
    private ModbusSiteMapper modbusSiteMapper;

    @Autowired
    private ModbusPointsMapper modbusPointsMapper;

    @Autowired
    private ModbusConfig modbusConfig;

    /**
     * 查询modbus站点详情
     * 
     * @param id modbus站点主键
     * @return modbus站点
     */
    @Override
    public ModbusSiteVO selectModbusSiteById(Long id)
    {
        ModbusSiteEntity entity = modbusSiteMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusSiteVO.class);
    }

    /**
     * 查询modbus站点列表
     * 
     * @param modbusSiteVO modbus站点
     * @return modbus站点
     */
    @Override
    public List<ModbusSiteVO> selectModbusSiteList(ModbusSiteVO modbusSiteVO)
    {
        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(getWrapper(modbusSiteVO));
        return PageObjectConvertUtil.convert(modbusSiteEntities,ModbusSiteVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusSiteEntity> getWrapper(ModbusSiteVO query){
        LambdaQueryWrapper<ModbusSiteEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusSiteEntity::getSiteId, query.getSiteId());
        wrapper.like(ObjUtil.isNotEmpty(query.getSiteName()), ModbusSiteEntity::getSiteName, query.getSiteName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDescription()), ModbusSiteEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMasterId()), ModbusSiteEntity::getMasterId, query.getMasterId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getHost()), ModbusSiteEntity::getHost, query.getHost());
        wrapper.eq(ObjUtil.isNotEmpty(query.getPort()), ModbusSiteEntity::getPort, query.getPort());
        wrapper.eq(ObjUtil.isNotEmpty(query.getKeepAlive()), ModbusSiteEntity::getKeepAlive, query.getKeepAlive());
        return wrapper;
    }

    /**
     * 新增modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int insertModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
       return modbusSiteMapper.insert(entity);
    }

    /**
     * 修改modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int updateModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
        return modbusSiteMapper.updateById(entity);
    }

    /**
     * 批量删除modbus站点
     * 
     * @param ids 需要删除的modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusSiteMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus站点信息
     * 
     * @param id modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteById(Long id)
    {

        return modbusSiteMapper.deleteById(id);
    }

    /**
     * 获取modbus站点列表相关配置
     *
     * @return
     */
    @Override
    public List<ModbusConfigData> getModbusConfigDataList() {

        List<ModbusConfigData> resList = new ArrayList<>();

        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(Wrappers.lambdaQuery());

        List<ModbusPointsEntity> modbusPointsEntities = modbusPointsMapper.selectList(
                Wrappers.lambdaQuery(ModbusPointsEntity.class).eq(ModbusPointsEntity::getStatus, 1));


        Map<Long, List<ModbusPointsEntity>> pointsGroupedByMasterId = modbusPointsEntities.stream()
                .collect(Collectors.groupingBy(ModbusPointsEntity::getMasterId));


        modbusSiteEntities.stream().forEach(item->{
            ModbusConfigData modbusConfigData = BeanUtil.toBean(item,ModbusConfigData.class);
            modbusConfigData.setPoints(pointsGroupedByMasterId.get(item.getMasterId()));
            resList.add(modbusConfigData);
        });



        return resList;
    }

    /**
     * 执行Modbus设备控制命令
     *
     * @param modbusCmdVO Modbus控制命令
     * @return 执行结果
     */
    @Override
    public boolean executeModbusCmd(ModbusCmdVO modbusCmdVO) {
        try {
            log.info("执行Modbus控制命令: 点位ID={}, 值={}, 命令类型={}, 描述={}",
                    modbusCmdVO.getPointId(), modbusCmdVO.getValue(),
                    modbusCmdVO.getCmdType(), modbusCmdVO.getDescription());

            // 根据pointId获取点位信息
            ModbusPointsEntity pointEntity = modbusPointsMapper.selectById(modbusCmdVO.getPointId());
            if (pointEntity == null) {
                log.error("未找到点位ID为 {} 的点位信息", modbusCmdVO.getPointId());
                return false;
            }

            // 获取Modbus客户端
            ModbusClient modbusClient = modbusConfig.getClientByMasterId(String.valueOf(pointEntity.getMasterId()));
            if (modbusClient == null) {
                log.error("未找到主站ID为 {} 的Modbus客户端", pointEntity.getMasterId());
                return false;
            }

            // 根据命令类型执行相应操作
            if (ModbusCmdVO.CmdType.WRITE.equals(modbusCmdVO.getCmdType())) {
                return executeWriteCommand(modbusClient, modbusCmdVO, pointEntity);
            } else if (ModbusCmdVO.CmdType.READ.equals(modbusCmdVO.getCmdType())) {
                return executeReadCommand(modbusClient, modbusCmdVO, pointEntity);
            } else {
                log.error("不支持的命令类型: {}", modbusCmdVO.getCmdType());
                return false;
            }

        } catch (Exception e) {
            log.error("执行Modbus控制命令失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行写入命令
     */
    private boolean executeWriteCommand(ModbusClient modbusClient, ModbusCmdVO modbusCmdVO, ModbusPointsEntity pointEntity) {
        try {
            String functionCode = pointEntity.getFunctionCode();
            int slaveId = pointEntity.getSlaveId().intValue();
            int registerAddress = pointEntity.getRegisterAddress();

            if ("1".equals(functionCode)) { // COIL_STATUS
                // 写入线圈
                boolean value = Boolean.parseBoolean(modbusCmdVO.getValue().toString());
                return Modbus4jUtils.writeCoil(modbusClient.getMaster(), slaveId, registerAddress, value);
            } else if ("3".equals(functionCode)) { // HOLDING_REGISTER
                // 写入保持寄存器
                if (modbusCmdVO.getValue() instanceof Number) {
                    Number value = (Number) modbusCmdVO.getValue();
                    Modbus4jUtils.writeHoldingRegister(modbusClient.getMaster(),
                            slaveId, registerAddress, value, getModbusDataType(pointEntity.getDataType()));
                    return true;
                } else {
                    // 尝试转换为数值
                    try {
                        int value = Integer.parseInt(modbusCmdVO.getValue().toString());
                        Modbus4jUtils.writeHoldingRegister(modbusClient.getMaster(),
                                slaveId, registerAddress, value, getModbusDataType(pointEntity.getDataType()));
                        return true;
                    } catch (NumberFormatException e) {
                        log.error("无法将值 {} 转换为数值", modbusCmdVO.getValue());
                        return false;
                    }
                }
            } else {
                log.error("不支持的功能码: {}", functionCode);
                return false;
            }
        } catch (ModbusTransportException | ModbusInitException e) {
            log.error("Modbus写入操作失败: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("执行写入命令异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行读取命令
     */
    private boolean executeReadCommand(ModbusClient modbusClient, ModbusCmdVO modbusCmdVO, ModbusPointsEntity pointEntity) {
        try {
            String functionCode = pointEntity.getFunctionCode();
            int slaveId = pointEntity.getSlaveId().intValue();
            int registerAddress = pointEntity.getRegisterAddress();

            if ("1".equals(functionCode)) { // COIL_STATUS
                // 读取线圈状态
                Boolean value = Modbus4jUtils.readCoilStatus(modbusClient.getMaster(), slaveId, registerAddress);
                log.info("读取线圈状态成功: 点位ID={}, 地址={}, 值={}", modbusCmdVO.getPointId(), registerAddress, value);
                return true;
            } else if ("3".equals(functionCode)) { // HOLDING_REGISTER
                // 读取保持寄存器
                Number value = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                        slaveId, registerAddress, getModbusDataType(pointEntity.getDataType()));
                log.info("读取保持寄存器成功: 点位ID={}, 地址={}, 值={}", modbusCmdVO.getPointId(), registerAddress, value);
                return true;
            } else if ("2".equals(functionCode)) { // INPUT_STATUS
                // 读取输入状态
                Boolean value = Modbus4jUtils.readInputStatus(modbusClient.getMaster(), slaveId, registerAddress);
                log.info("读取输入状态成功: 点位ID={}, 地址={}, 值={}", modbusCmdVO.getPointId(), registerAddress, value);
                return true;
            } else if ("4".equals(functionCode)) { // INPUT_REGISTER
                // 读取输入寄存器
                Number value = Modbus4jUtils.readInputRegisters(modbusClient.getMaster(),
                        slaveId, registerAddress, getModbusDataType(pointEntity.getDataType()));
                log.info("读取输入寄存器成功: 点位ID={}, 地址={}, 值={}", modbusCmdVO.getPointId(), registerAddress, value);
                return true;
            } else {
                log.error("不支持的功能码: {}", functionCode);
                return false;
            }
        } catch (Exception e) {
            log.error("Modbus读取操作失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据数据类型字符串获取Modbus数据类型
     */
    private int getModbusDataType(String dataType) {
        if ("2".equals(dataType)) {
            return DataType.TWO_BYTE_INT_UNSIGNED;
        } else if ("3".equals(dataType)) {
            return DataType.TWO_BYTE_INT_SIGNED;
        } else {
            // 默认使用无符号16位整数
            return DataType.TWO_BYTE_INT_UNSIGNED;
        }
    }

    /**
     * 获取Modbus设备控制面板状态
     *
     * @param siteId 站点ID
     * @return 控制面板状态
     */
    @Override
    public ModbusControlPanelVO getControlPanelStatus(Long siteId) {
        ModbusControlPanelVO controlPanel = new ModbusControlPanelVO();

        try {
            // 获取站点信息
            ModbusSiteEntity siteEntity = modbusSiteMapper.selectOne(
                    Wrappers.lambdaQuery(ModbusSiteEntity.class)
                            .eq(ModbusSiteEntity::getSiteId, siteId)
                            .last("LIMIT 1"));

            if (siteEntity == null) {
                log.error("未找到站点ID为 {} 的站点信息", siteId);
                return controlPanel;
            }

            controlPanel.setSiteId(siteId);
            controlPanel.setSiteName(siteEntity.getSiteName());

            // 获取Modbus客户端
            ModbusClient modbusClient = modbusConfig.getClientByMasterId(String.valueOf(siteEntity.getMasterId()));
            if (modbusClient == null) {
                log.error("未找到主站ID为 {} 的Modbus客户端", siteEntity.getMasterId());
                return controlPanel;
            }

            // 读取各个控制点的状态
            readControlPanelData(modbusClient, controlPanel, siteEntity.getMasterId().intValue());

        } catch (Exception e) {
            log.error("获取控制面板状态失败: {}", e.getMessage(), e);
        }

        return controlPanel;
    }

    /**
     * 读取控制面板数据
     */
    private void readControlPanelData(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 读取操作模式
            Number operationMode = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.OPERATION_MODE, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setOperationMode(operationMode != null ? operationMode.intValue() : 0);

            // 读取运行状态
            Number runningStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.RUNNING_STATUS, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setRunningStatus(runningStatus != null ? runningStatus.intValue() : 0);

            // 读取变频器启停状态
            Number inverterStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INVERTER_START_STOP, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInverterStatus(inverterStatus != null ? inverterStatus.intValue() : 0);

            // 读取变频器故障状态
            Number inverterFaultStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INVERTER_FAULT_RESET, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInverterFaultStatus(inverterFaultStatus != null ? inverterFaultStatus.intValue() : 0);

            // 读取手动模式控制状态
            if (controlPanel.isManualMode()) {
                readManualModeData(modbusClient, controlPanel, slaveId);
            }

            // 读取参数设定
            readParameterSettings(modbusClient, controlPanel, slaveId);

        } catch (Exception e) {
            log.error("读取控制面板数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取手动模式控制数据
     */
    private void readManualModeData(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 旁路风阀状态
            Number bypassDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.BYPASS_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setBypassDamperStatus(bypassDamper != null ? bypassDamper.intValue() : 0);

            // 空气预处理器状态
            Number airPretreatment = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_START, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentStatus(airPretreatment != null ? airPretreatment.intValue() : 0);

            // 除湿机状态
            Number dehumidifier = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.DEHUMIDIFIER_START, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setDehumidifierStatus(dehumidifier != null ? dehumidifier.intValue() : 0);

            // 进风阀状态
            Number inletDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INLET_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInletDamperStatus(inletDamper != null ? inletDamper.intValue() : 0);

            // 出风口风阀状态
            Number outletDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.OUTLET_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setOutletDamperStatus(outletDamper != null ? outletDamper.intValue() : 0);

            // 空气预处理器风机状态
            Number airPretreatmentFan = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_FAN, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentFanStatus(airPretreatmentFan != null ? airPretreatmentFan.intValue() : 0);

            // 空气预处理器风速
            Number airPretreatmentSpeed = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_SPEED, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentSpeed(airPretreatmentSpeed != null ? airPretreatmentSpeed.intValue() : 0);

        } catch (Exception e) {
            log.error("读取手动模式控制数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取参数设定
     */
    private void readParameterSettings(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 温度设定
            Number temperatureSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.TEMPERATURE_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setTemperatureSetting(temperatureSetting != null ? temperatureSetting.intValue() : 0);

            // 湿度设定
            Number humiditySetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.HUMIDITY_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setHumiditySetting(humiditySetting != null ? humiditySetting.intValue() : 0);

            // 模式设定
            Number modeSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.MODE_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setModeSetting(modeSetting != null ? modeSetting.intValue() : 0);

            // 开放能需设定
            Number energyDemandSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.ENERGY_DEMAND_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setEnergyDemandSetting(energyDemandSetting != null ? energyDemandSetting.intValue() : 0);

        } catch (Exception e) {
            log.error("读取参数设定失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 批量查询Modbus点位状态
     *
     * @param pointIds 点位ID列表
     * @return 点位状态列表
     */
    @Override
    public List<ModbusPointValueVO> getPointStatus(List<Long> pointIds) {
        List<ModbusPointValueVO> result = new ArrayList<>();

        if (pointIds == null || pointIds.isEmpty()) {
            return result;
        }

        try {
            // 查询所有点位信息
            List<ModbusPointsEntity> pointEntities = modbusPointsMapper.selectBatchIds(pointIds);

            // 按主站分组
            Map<Long, List<ModbusPointsEntity>> pointsByMaster = pointEntities.stream()
                    .collect(Collectors.groupingBy(ModbusPointsEntity::getMasterId));

            // 逐个主站读取数据
            for (Map.Entry<Long, List<ModbusPointsEntity>> entry : pointsByMaster.entrySet()) {
                Long masterId = entry.getKey();
                List<ModbusPointsEntity> points = entry.getValue();

                // 获取Modbus客户端
                ModbusClient modbusClient = modbusConfig.getClientByMasterId(String.valueOf(masterId));
                if (modbusClient == null) {
                    log.error("未找到主站ID为 {} 的Modbus客户端", masterId);
                    // 添加失败记录
                    for (ModbusPointsEntity point : points) {
                        ModbusPointValueVO valueVO = createFailedPointValue(point, "未找到Modbus客户端");
                        result.add(valueVO);
                    }
                    continue;
                }

                // 读取每个点位的值
                for (ModbusPointsEntity point : points) {
                    ModbusPointValueVO valueVO = readSinglePointValue(modbusClient, point);
                    result.add(valueVO);
                }
            }

        } catch (Exception e) {
            log.error("批量查询点位状态失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 读取单个点位的值
     */
    private ModbusPointValueVO readSinglePointValue(ModbusClient modbusClient, ModbusPointsEntity pointEntity) {
        ModbusPointValueVO valueVO = new ModbusPointValueVO();
        valueVO.setPointId(pointEntity.getId());
        valueVO.setPointName(pointEntity.getPointName());
        valueVO.setDescription(pointEntity.getDescription());
        valueVO.setFunctionCode(pointEntity.getFunctionCode());
        valueVO.setRegisterAddress(pointEntity.getRegisterAddress());

        try {
            String functionCode = pointEntity.getFunctionCode();
            int slaveId = pointEntity.getSlaveId().intValue();
            int registerAddress = pointEntity.getRegisterAddress();
            int dataType = getModbusDataType(pointEntity.getDataType());

            Object value = null;

            if ("1".equals(functionCode)) { // COIL_STATUS
                value = Modbus4jUtils.readCoilStatus(modbusClient.getMaster(), slaveId, registerAddress);
            } else if ("2".equals(functionCode)) { // INPUT_STATUS
                value = Modbus4jUtils.readInputStatus(modbusClient.getMaster(), slaveId, registerAddress);
            } else if ("3".equals(functionCode)) { // HOLDING_REGISTER
                value = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(), slaveId, registerAddress, dataType);
            } else if ("4".equals(functionCode)) { // INPUT_REGISTER
                value = Modbus4jUtils.readInputRegisters(modbusClient.getMaster(), slaveId, registerAddress, dataType);
            } else {
                throw new RuntimeException("不支持的功能码: " + functionCode);
            }

            valueVO.setValue(value);
            valueVO.setSuccess(true);

            log.debug("读取点位成功: pointId={}, value={}", pointEntity.getId(), value);

        } catch (Exception e) {
            log.error("读取点位 {} 失败: {}", pointEntity.getId(), e.getMessage(), e);
            valueVO.setValue(null);
            valueVO.setSuccess(false);
            valueVO.setErrorMessage(e.getMessage());
        }

        return valueVO;
    }

    /**
     * 创建失败的点位值记录
     */
    private ModbusPointValueVO createFailedPointValue(ModbusPointsEntity pointEntity, String errorMessage) {
        ModbusPointValueVO valueVO = new ModbusPointValueVO();
        valueVO.setPointId(pointEntity.getId());
        valueVO.setPointName(pointEntity.getPointName());
        valueVO.setDescription(pointEntity.getDescription());
        valueVO.setFunctionCode(pointEntity.getFunctionCode());
        valueVO.setRegisterAddress(pointEntity.getRegisterAddress());
        valueVO.setValue(null);
        valueVO.setSuccess(false);
        valueVO.setErrorMessage(errorMessage);
        return valueVO;
    }

}
