package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tianmasys.ebu.qingma.domain.ModbusConfigData;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;
import com.tianmasys.ebu.qingma.mapper.ModbusPointsMapper;
import com.tianmasys.ebu.qingma.service.IModbusPointsService;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import com.tianmasys.ebu.qingma.vo.ModbusCmdVO;
import com.tianmasys.ebu.qingma.vo.ModbusControlPanelVO;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.ModbusConfig;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.utils.Modbus4jUtils;
import com.serotonin.modbus4j.code.DataType;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusSiteMapper;
import com.tianmasys.ebu.qingma.service.IModbusSiteService;
import com.tianmasys.ebu.qingma.vo.ModbusSiteVO;
import com.tianmasys.ebu.qingma.domain.ModbusSiteEntity;

/**
 * modbus站点Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Slf4j
@Service
public class ModbusSiteServiceImpl extends ServiceImpl<ModbusSiteMapper,ModbusSiteEntity> implements IModbusSiteService
{
    @Autowired
    private ModbusSiteMapper modbusSiteMapper;

    @Autowired
    private ModbusPointsMapper modbusPointsMapper;

    @Autowired
    private ModbusConfig modbusConfig;

    /**
     * 查询modbus站点详情
     * 
     * @param id modbus站点主键
     * @return modbus站点
     */
    @Override
    public ModbusSiteVO selectModbusSiteById(Long id)
    {
        ModbusSiteEntity entity = modbusSiteMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusSiteVO.class);
    }

    /**
     * 查询modbus站点列表
     * 
     * @param modbusSiteVO modbus站点
     * @return modbus站点
     */
    @Override
    public List<ModbusSiteVO> selectModbusSiteList(ModbusSiteVO modbusSiteVO)
    {
        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(getWrapper(modbusSiteVO));
        return PageObjectConvertUtil.convert(modbusSiteEntities,ModbusSiteVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusSiteEntity> getWrapper(ModbusSiteVO query){
        LambdaQueryWrapper<ModbusSiteEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusSiteEntity::getSiteId, query.getSiteId());
        wrapper.like(ObjUtil.isNotEmpty(query.getSiteName()), ModbusSiteEntity::getSiteName, query.getSiteName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDescription()), ModbusSiteEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMasterId()), ModbusSiteEntity::getMasterId, query.getMasterId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getHost()), ModbusSiteEntity::getHost, query.getHost());
        wrapper.eq(ObjUtil.isNotEmpty(query.getPort()), ModbusSiteEntity::getPort, query.getPort());
        wrapper.eq(ObjUtil.isNotEmpty(query.getKeepAlive()), ModbusSiteEntity::getKeepAlive, query.getKeepAlive());
        return wrapper;
    }

    /**
     * 新增modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int insertModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
       return modbusSiteMapper.insert(entity);
    }

    /**
     * 修改modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int updateModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
        return modbusSiteMapper.updateById(entity);
    }

    /**
     * 批量删除modbus站点
     * 
     * @param ids 需要删除的modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusSiteMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus站点信息
     * 
     * @param id modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteById(Long id)
    {

        return modbusSiteMapper.deleteById(id);
    }

    /**
     * 获取modbus站点列表相关配置
     *
     * @return
     */
    @Override
    public List<ModbusConfigData> getModbusConfigDataList() {

        List<ModbusConfigData> resList = new ArrayList<>();

        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(Wrappers.lambdaQuery());

        List<ModbusPointsEntity> modbusPointsEntities = modbusPointsMapper.selectList(
                Wrappers.lambdaQuery(ModbusPointsEntity.class).eq(ModbusPointsEntity::getStatus, 1));


        Map<Long, List<ModbusPointsEntity>> pointsGroupedByMasterId = modbusPointsEntities.stream()
                .collect(Collectors.groupingBy(ModbusPointsEntity::getMasterId));


        modbusSiteEntities.stream().forEach(item->{
            ModbusConfigData modbusConfigData = BeanUtil.toBean(item,ModbusConfigData.class);
            modbusConfigData.setPoints(pointsGroupedByMasterId.get(item.getMasterId()));
            resList.add(modbusConfigData);
        });



        return resList;
    }

    /**
     * 执行Modbus设备控制命令
     *
     * @param modbusCmdVO Modbus控制命令
     * @return 执行结果
     */
    @Override
    public boolean executeModbusCmd(ModbusCmdVO modbusCmdVO) {
        try {
            log.info("执行Modbus控制命令: 站点ID={}, 主站ID={}, 从站ID={}, 地址={}, 值={}, 数据类型={}, 命令类型={}",
                    modbusCmdVO.getSiteId(), modbusCmdVO.getMasterId(), modbusCmdVO.getSlaveId(),
                    modbusCmdVO.getRegisterAddress(), modbusCmdVO.getValue(),
                    modbusCmdVO.getDataType(), modbusCmdVO.getCmdType());

            // 获取Modbus客户端
            ModbusClient modbusClient = modbusConfig.getClientByMasterId(String.valueOf(modbusCmdVO.getMasterId()));
            if (modbusClient == null) {
                log.error("未找到主站ID为 {} 的Modbus客户端", modbusCmdVO.getMasterId());
                return false;
            }

            // 根据命令类型执行相应操作
            if (ModbusCmdVO.CmdType.WRITE.equals(modbusCmdVO.getCmdType())) {
                return executeWriteCommand(modbusClient, modbusCmdVO);
            } else if (ModbusCmdVO.CmdType.READ.equals(modbusCmdVO.getCmdType())) {
                return executeReadCommand(modbusClient, modbusCmdVO);
            } else {
                log.error("不支持的命令类型: {}", modbusCmdVO.getCmdType());
                return false;
            }

        } catch (Exception e) {
            log.error("执行Modbus控制命令失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行写入命令
     */
    private boolean executeWriteCommand(ModbusClient modbusClient, ModbusCmdVO modbusCmdVO) {
        try {
            if (ModbusCmdVO.DataType.COIL.equals(modbusCmdVO.getDataType())) {
                // 写入线圈
                boolean value = Boolean.parseBoolean(modbusCmdVO.getValue().toString());
                return Modbus4jUtils.writeCoil(modbusClient.getMaster(),
                        modbusCmdVO.getSlaveId(),
                        modbusCmdVO.getRegisterAddress(),
                        value);
            } else if (ModbusCmdVO.DataType.HOLDING_REGISTER.equals(modbusCmdVO.getDataType())) {
                // 写入保持寄存器
                if (modbusCmdVO.getValue() instanceof Number) {
                    Number value = (Number) modbusCmdVO.getValue();
                    Modbus4jUtils.writeHoldingRegister(modbusClient.getMaster(),
                            modbusCmdVO.getSlaveId(),
                            modbusCmdVO.getRegisterAddress(),
                            value,
                            DataType.TWO_BYTE_INT_UNSIGNED);
                    return true;
                } else {
                    // 尝试转换为数值
                    try {
                        int value = Integer.parseInt(modbusCmdVO.getValue().toString());
                        Modbus4jUtils.writeHoldingRegister(modbusClient.getMaster(),
                                modbusCmdVO.getSlaveId(),
                                modbusCmdVO.getRegisterAddress(),
                                value,
                                DataType.TWO_BYTE_INT_UNSIGNED);
                        return true;
                    } catch (NumberFormatException e) {
                        log.error("无法将值 {} 转换为数值", modbusCmdVO.getValue());
                        return false;
                    }
                }
            } else {
                log.error("不支持的数据类型: {}", modbusCmdVO.getDataType());
                return false;
            }
        } catch (ModbusTransportException | ModbusInitException e) {
            log.error("Modbus写入操作失败: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("执行写入命令异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行读取命令
     */
    private boolean executeReadCommand(ModbusClient modbusClient, ModbusCmdVO modbusCmdVO) {
        try {
            if (ModbusCmdVO.DataType.COIL.equals(modbusCmdVO.getDataType())) {
                // 读取线圈状态
                Boolean value = Modbus4jUtils.readCoilStatus(modbusClient.getMaster(),
                        modbusCmdVO.getSlaveId(),
                        modbusCmdVO.getRegisterAddress());
                log.info("读取线圈状态成功: 地址={}, 值={}", modbusCmdVO.getRegisterAddress(), value);
                return true;
            } else if (ModbusCmdVO.DataType.HOLDING_REGISTER.equals(modbusCmdVO.getDataType())) {
                // 读取保持寄存器
                Number value = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                        modbusCmdVO.getSlaveId(),
                        modbusCmdVO.getRegisterAddress(),
                        DataType.TWO_BYTE_INT_UNSIGNED);
                log.info("读取保持寄存器成功: 地址={}, 值={}", modbusCmdVO.getRegisterAddress(), value);
                return true;
            } else {
                log.error("不支持的数据类型: {}", modbusCmdVO.getDataType());
                return false;
            }
        } catch (Exception e) {
            log.error("Modbus读取操作失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取Modbus设备控制面板状态
     *
     * @param siteId 站点ID
     * @return 控制面板状态
     */
    @Override
    public ModbusControlPanelVO getControlPanelStatus(Long siteId) {
        ModbusControlPanelVO controlPanel = new ModbusControlPanelVO();

        try {
            // 获取站点信息
            ModbusSiteEntity siteEntity = modbusSiteMapper.selectOne(
                    Wrappers.lambdaQuery(ModbusSiteEntity.class)
                            .eq(ModbusSiteEntity::getSiteId, siteId)
                            .last("LIMIT 1"));

            if (siteEntity == null) {
                log.error("未找到站点ID为 {} 的站点信息", siteId);
                return controlPanel;
            }

            controlPanel.setSiteId(siteId);
            controlPanel.setSiteName(siteEntity.getSiteName());

            // 获取Modbus客户端
            ModbusClient modbusClient = modbusConfig.getClientByMasterId(String.valueOf(siteEntity.getMasterId()));
            if (modbusClient == null) {
                log.error("未找到主站ID为 {} 的Modbus客户端", siteEntity.getMasterId());
                return controlPanel;
            }

            // 读取各个控制点的状态
            readControlPanelData(modbusClient, controlPanel, siteEntity.getMasterId().intValue());

        } catch (Exception e) {
            log.error("获取控制面板状态失败: {}", e.getMessage(), e);
        }

        return controlPanel;
    }

    /**
     * 读取控制面板数据
     */
    private void readControlPanelData(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 读取操作模式
            Number operationMode = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.OPERATION_MODE, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setOperationMode(operationMode != null ? operationMode.intValue() : 0);

            // 读取运行状态
            Number runningStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.RUNNING_STATUS, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setRunningStatus(runningStatus != null ? runningStatus.intValue() : 0);

            // 读取变频器启停状态
            Number inverterStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INVERTER_START_STOP, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInverterStatus(inverterStatus != null ? inverterStatus.intValue() : 0);

            // 读取变频器故障状态
            Number inverterFaultStatus = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INVERTER_FAULT_RESET, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInverterFaultStatus(inverterFaultStatus != null ? inverterFaultStatus.intValue() : 0);

            // 读取手动模式控制状态
            if (controlPanel.isManualMode()) {
                readManualModeData(modbusClient, controlPanel, slaveId);
            }

            // 读取参数设定
            readParameterSettings(modbusClient, controlPanel, slaveId);

        } catch (Exception e) {
            log.error("读取控制面板数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取手动模式控制数据
     */
    private void readManualModeData(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 旁路风阀状态
            Number bypassDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.BYPASS_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setBypassDamperStatus(bypassDamper != null ? bypassDamper.intValue() : 0);

            // 空气预处理器状态
            Number airPretreatment = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_START, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentStatus(airPretreatment != null ? airPretreatment.intValue() : 0);

            // 除湿机状态
            Number dehumidifier = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.DEHUMIDIFIER_START, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setDehumidifierStatus(dehumidifier != null ? dehumidifier.intValue() : 0);

            // 进风阀状态
            Number inletDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.INLET_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setInletDamperStatus(inletDamper != null ? inletDamper.intValue() : 0);

            // 出风口风阀状态
            Number outletDamper = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.OUTLET_DAMPER, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setOutletDamperStatus(outletDamper != null ? outletDamper.intValue() : 0);

            // 空气预处理器风机状态
            Number airPretreatmentFan = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_FAN, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentFanStatus(airPretreatmentFan != null ? airPretreatmentFan.intValue() : 0);

            // 空气预处理器风速
            Number airPretreatmentSpeed = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_SPEED, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setAirPretreatmentSpeed(airPretreatmentSpeed != null ? airPretreatmentSpeed.intValue() : 0);

        } catch (Exception e) {
            log.error("读取手动模式控制数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取参数设定
     */
    private void readParameterSettings(ModbusClient modbusClient, ModbusControlPanelVO controlPanel, int slaveId) {
        try {
            // 温度设定
            Number temperatureSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.TEMPERATURE_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setTemperatureSetting(temperatureSetting != null ? temperatureSetting.intValue() : 0);

            // 湿度设定
            Number humiditySetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.HUMIDITY_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setHumiditySetting(humiditySetting != null ? humiditySetting.intValue() : 0);

            // 模式设定
            Number modeSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.MODE_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setModeSetting(modeSetting != null ? modeSetting.intValue() : 0);

            // 开放能需设定
            Number energyDemandSetting = Modbus4jUtils.readHoldingRegister(modbusClient.getMaster(),
                    slaveId, ModbusCmdVO.ControlAddress.ENERGY_DEMAND_SETTING, DataType.TWO_BYTE_INT_UNSIGNED);
            controlPanel.setEnergyDemandSetting(energyDemandSetting != null ? energyDemandSetting.intValue() : 0);

        } catch (Exception e) {
            log.error("读取参数设定失败: {}", e.getMessage(), e);
        }
    }

}
