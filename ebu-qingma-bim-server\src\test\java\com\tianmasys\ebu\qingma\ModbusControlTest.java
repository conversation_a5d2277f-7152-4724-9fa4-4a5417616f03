package com.tianmasys.ebu.qingma;

import com.tianmasys.ebu.qingma.vo.ModbusCmdVO;
import com.tianmasys.ebu.qingma.vo.ModbusControlPanelVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Modbus设备控制功能测试
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Slf4j
@SpringBootTest
public class ModbusControlTest {

    @Test
    public void testModbusCmdVO() {
        // 测试控制命令VO
        ModbusCmdVO cmdVO = new ModbusCmdVO();
        cmdVO.setSiteId(1L);
        cmdVO.setMasterId(1L);
        cmdVO.setSlaveId(1);
        cmdVO.setRegisterAddress(ModbusCmdVO.ControlAddress.OPERATION_MODE);
        cmdVO.setValue(1);
        cmdVO.setDataType(ModbusCmdVO.DataType.HOLDING_REGISTER);
        cmdVO.setCmdType(ModbusCmdVO.CmdType.WRITE);
        cmdVO.setDescription("设置操作模式为自动");

        log.info("ModbusCmdVO测试: {}", cmdVO);
        
        // 验证常量
        assert ModbusCmdVO.ControlAddress.OPERATION_MODE == 1001;
        assert ModbusCmdVO.ControlAddress.TEMPERATURE_SETTING == 1020;
        assert ModbusCmdVO.ModeValue.COOLING == 1;
        assert ModbusCmdVO.ModeValue.AUTO == 6;
        
        log.info("ModbusCmdVO常量验证通过");
    }

    @Test
    public void testModbusControlPanelVO() {
        // 测试控制面板VO
        ModbusControlPanelVO panelVO = new ModbusControlPanelVO();
        panelVO.setSiteId(1L);
        panelVO.setSiteName("测试站点");
        panelVO.setOperationMode(ModbusControlPanelVO.OperationMode.MANUAL);
        panelVO.setRunningStatus(ModbusControlPanelVO.DeviceStatus.START);
        panelVO.setInverterStatus(ModbusControlPanelVO.DeviceStatus.START);
        panelVO.setInverterFaultStatus(ModbusControlPanelVO.DeviceStatus.STOP);
        panelVO.setTemperatureSetting(25);
        panelVO.setHumiditySetting(60);
        panelVO.setModeSetting(ModbusControlPanelVO.ModeType.COOLING);

        log.info("ModbusControlPanelVO测试: {}", panelVO);
        
        // 验证辅助方法
        assert panelVO.isManualMode() == true;
        assert panelVO.isAutoMode() == false;
        assert panelVO.isRunning() == true;
        assert panelVO.hasInverterFault() == false;
        
        log.info("ModbusControlPanelVO辅助方法验证通过");
    }

    @Test
    public void testControlAddressMapping() {
        // 测试控制地址映射
        log.info("=== 状态显示区域地址 ===");
        log.info("操作模式: {}", ModbusCmdVO.ControlAddress.OPERATION_MODE);
        log.info("运行状态: {}", ModbusCmdVO.ControlAddress.RUNNING_STATUS);
        log.info("变频器启停: {}", ModbusCmdVO.ControlAddress.INVERTER_START_STOP);
        log.info("变频器故障复位: {}", ModbusCmdVO.ControlAddress.INVERTER_FAULT_RESET);

        log.info("=== 手动模式控制区域地址 ===");
        log.info("旁路风阀: {}", ModbusCmdVO.ControlAddress.BYPASS_DAMPER);
        log.info("空气预处理器: {}", ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_START);
        log.info("除湿机: {}", ModbusCmdVO.ControlAddress.DEHUMIDIFIER_START);
        log.info("进风阀: {}", ModbusCmdVO.ControlAddress.INLET_DAMPER);
        log.info("出风口风阀: {}", ModbusCmdVO.ControlAddress.OUTLET_DAMPER);
        log.info("空气预处理器风机: {}", ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_FAN);
        log.info("空气预处理器风速: {}", ModbusCmdVO.ControlAddress.AIR_PRETREATMENT_SPEED);

        log.info("=== 参数设定区域地址 ===");
        log.info("温度设定: {}", ModbusCmdVO.ControlAddress.TEMPERATURE_SETTING);
        log.info("湿度设定: {}", ModbusCmdVO.ControlAddress.HUMIDITY_SETTING);
        log.info("模式设定: {}", ModbusCmdVO.ControlAddress.MODE_SETTING);
        log.info("开放能需设定: {}", ModbusCmdVO.ControlAddress.ENERGY_DEMAND_SETTING);

        log.info("=== 模式设定值 ===");
        log.info("制冷运行: {}", ModbusCmdVO.ModeValue.COOLING);
        log.info("制热运行: {}", ModbusCmdVO.ModeValue.HEATING);
        log.info("热源制热运行: {}", ModbusCmdVO.ModeValue.HEAT_SOURCE);
        log.info("通风运行: {}", ModbusCmdVO.ModeValue.VENTILATION);
        log.info("自动: {}", ModbusCmdVO.ModeValue.AUTO);
    }

    @Test
    public void testCreateControlCommands() {
        log.info("=== 创建常用控制命令示例 ===");
        
        // 1. 切换到手动模式
        ModbusCmdVO manualModeCmd = createControlCmd(1L, 1L, 1, 
                ModbusCmdVO.ControlAddress.OPERATION_MODE, 0, "切换到手动模式");
        log.info("手动模式命令: {}", manualModeCmd);

        // 2. 启动变频器
        ModbusCmdVO startInverterCmd = createControlCmd(1L, 1L, 1, 
                ModbusCmdVO.ControlAddress.INVERTER_START_STOP, 1, "启动变频器");
        log.info("启动变频器命令: {}", startInverterCmd);

        // 3. 开启旁路风阀
        ModbusCmdVO openBypassCmd = createControlCmd(1L, 1L, 1, 
                ModbusCmdVO.ControlAddress.BYPASS_DAMPER, 1, "开启旁路风阀");
        log.info("开启旁路风阀命令: {}", openBypassCmd);

        // 4. 设置温度
        ModbusCmdVO setTempCmd = createControlCmd(1L, 1L, 1, 
                ModbusCmdVO.ControlAddress.TEMPERATURE_SETTING, 25, "设置温度为25℃");
        log.info("设置温度命令: {}", setTempCmd);

        // 5. 设置制冷模式
        ModbusCmdVO coolingModeCmd = createControlCmd(1L, 1L, 1, 
                ModbusCmdVO.ControlAddress.MODE_SETTING, ModbusCmdVO.ModeValue.COOLING, "设置制冷模式");
        log.info("设置制冷模式命令: {}", coolingModeCmd);
    }

    /**
     * 创建控制命令的辅助方法
     */
    private ModbusCmdVO createControlCmd(Long siteId, Long masterId, Integer slaveId, 
                                       Integer address, Object value, String description) {
        ModbusCmdVO cmd = new ModbusCmdVO();
        cmd.setSiteId(siteId);
        cmd.setMasterId(masterId);
        cmd.setSlaveId(slaveId);
        cmd.setRegisterAddress(address);
        cmd.setValue(value);
        cmd.setDataType(ModbusCmdVO.DataType.HOLDING_REGISTER);
        cmd.setCmdType(ModbusCmdVO.CmdType.WRITE);
        cmd.setDescription(description);
        return cmd;
    }
}
