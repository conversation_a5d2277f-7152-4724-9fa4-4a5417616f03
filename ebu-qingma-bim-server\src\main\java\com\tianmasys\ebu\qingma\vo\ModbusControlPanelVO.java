package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

/**
 * Modbus设备控制面板VO
 * 用于Vue前端控制面板显示和操作
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "Modbus设备控制面板")
public class ModbusControlPanelVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "站点ID")
    private Long siteId;

    @Schema(description = "站点名称")
    private String siteName;

    // ========== 状态显示区域 ==========
    @Schema(description = "操作模式：0-手动，1-自动")
    private Integer operationMode;

    @Schema(description = "运行状态：0-停止，1-运行")
    private Integer runningStatus;

    @Schema(description = "变频器启停状态：0-停止，1-启动")
    private Integer inverterStatus;

    @Schema(description = "变频器故障状态：0-正常，1-故障")
    private Integer inverterFaultStatus;

    // ========== 手动模式控制区域 ==========
    @Schema(description = "旁路风阀状态：0-关闭，1-开启")
    private Integer bypassDamperStatus;

    @Schema(description = "空气预处理器状态：0-停止，1-启动")
    private Integer airPretreatmentStatus;

    @Schema(description = "除湿机状态：0-停止，1-启动")
    private Integer dehumidifierStatus;

    @Schema(description = "进风阀状态：0-关闭，1-开启")
    private Integer inletDamperStatus;

    @Schema(description = "出风口风阀状态：0-关闭，1-开启")
    private Integer outletDamperStatus;

    @Schema(description = "空气预处理器风机状态：0-停止，1-启动")
    private Integer airPretreatmentFanStatus;

    @Schema(description = "空气预处理器风速：0-100")
    private Integer airPretreatmentSpeed;

    // ========== 参数设定区域 ==========
    @Schema(description = "温度设定值（摄氏度）")
    private Integer temperatureSetting;

    @Schema(description = "湿度设定值（百分比）")
    private Integer humiditySetting;

    @Schema(description = "模式设定：1-制冷运行，2-制热运行，3-热源制热运行，4-通风运行，6-自动")
    private Integer modeSetting;

    @Schema(description = "开放能需设定：0-1000")
    private Integer energyDemandSetting;

    // ========== 辅助信息 ==========
    @Schema(description = "是否为手动模式")
    public boolean isManualMode() {
        return operationMode != null && operationMode == 0;
    }

    @Schema(description = "是否为自动模式")
    public boolean isAutoMode() {
        return operationMode != null && operationMode == 1;
    }

    @Schema(description = "是否运行中")
    public boolean isRunning() {
        return runningStatus != null && runningStatus == 1;
    }

    @Schema(description = "变频器是否有故障")
    public boolean hasInverterFault() {
        return inverterFaultStatus != null && inverterFaultStatus == 1;
    }

    // ========== 模式设定常量 ==========
    public static class ModeType {
        public static final int COOLING = 1;        // 制冷运行
        public static final int HEATING = 2;        // 制热运行
        public static final int HEAT_SOURCE = 3;    // 热源制热运行
        public static final int VENTILATION = 4;    // 通风运行
        public static final int AUTO = 6;           // 自动
    }

    // ========== 操作模式常量 ==========
    public static class OperationMode {
        public static final int MANUAL = 0;         // 手动模式
        public static final int AUTO = 1;           // 自动模式
    }

    // ========== 设备状态常量 ==========
    public static class DeviceStatus {
        public static final int STOP = 0;           // 停止/关闭
        public static final int START = 1;          // 启动/开启
    }
}
