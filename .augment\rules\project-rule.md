---
type: "always_apply"
---

# 青马大桥BIM系统开发规则

## 项目概述
基于Spring Boot的Modbus数据采集与实时监控平台，主要功能：Modbus设备数据采集、WebSocket实时推送、数据统计分析、设备管理监控。

技术栈：Spring Boot 2.x + JDK 1.8 + MySQL + MyBatis Plus + Modbus4j + WebSocket + MQTT + Maven

## 代码结构规范

### 包结构
```
com.tianmasys.ebu.qingma/
├── config/          # 配置类(WebSocket、Feign等)
├── controller/      # 控制器(app/移动端、common/通用)
├── dataprocess/     # 数据处理(modbus/、emq/)
├── domain/          # 实体类
├── service/         # 业务层(impl/实现类)
├── mapper/          # 数据访问层
├── statistics/      # 统计处理
├── job/            # 定时任务
├── utils/          # 工具类
├── vo/             # 视图对象
└── constant/       # 常量定义
```

### 命名规范
- 实体类: `XxxEntity.java`
- VO类: `XxxVO.java`
- 控制器: `XxxController.java`
- 服务接口: `IXxxService.java`
- 服务实现: `XxxServiceImpl.java`
- Mapper: `XxxMapper.java`
- 配置类: `XxxConfig.java`

### 数据库命名
- 表名: 小写+下划线，前缀`iot_`(设备相关)/`modbus_`(Modbus相关)
- 标准字段: `id`(主键)、`create_time`、`update_time`、`create_by`、`update_by`、`delete_flag`

## 代码规范

### 注解使用
```java
// 实体类
@Data
@TableName("iot_device")
public class DeviceEntity extends BaseEntity {
    @TableId
    private Long id;
}

// 服务类
@Slf4j
@Service
public class DeviceServiceImpl implements IDeviceService {}

// 控制器
@RestController
@RequestMapping("/iot/device")
public class DeviceController {}

// 配置类
@Configuration
@EnableWebSocket
public class WebSocketConfig {}
```

### 异常处理
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("操作失败: {}", e.getMessage(), e);
    throw new ServiceException("操作失败");
}
```

### 日志规范
```java
log.info("Modbus数据读取成功, 站点: {}, 数据量: {}", siteId, dataSize);
log.error("WebSocket发送失败: {}", e.getMessage(), e);
log.debug("处理站点 {} 数据: {}", siteId, data);
```

## 核心业务规范

### Modbus数据采集
```java
// 定时任务注册
@PostConstruct
public void startScheduledTask() {
    taskSchedulerManager.registerFixedRateTask("modbusDataJob",
        this::readModbusDataAndSend, 0, 1000);
}

// 数据格式: address_1001: value
// 使用Modbus4jUtils工具类进行批量读取
// 支持异常处理和重连机制
```

### WebSocket推送
```java
// 配置
@Override
public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
    registry.addHandler(webSocketHandler, "/ws")
            .setAllowedOriginPatterns("*").withSockJS();
}

// 推送数据格式
{
    "timestamp": "2025-08-08T10:30:00",
    "data": {
        "siteId_1": {
            "address_1001": 100,
            "address_1002": 200
        }
    }
}

// 会话管理: 使用ConcurrentHashMap存储，自动清理断开连接
```

### 统计数据处理
```java
// 统计类型: minute(分钟)/hour(小时)/day(天)
// 统计指标: avg(平均值)、last(最新值)、count(数据点数量)
ModbusDataStatisticsEntity entity = new ModbusDataStatisticsEntity();
entity.setSiteId(siteId);
entity.setStatType("minute"); // minute/hour/day
entity.setStartTime(periodStartTime);
entity.setEndTime(periodEndTime);
entity.setData(statResult);
```

## 配置规范

### 多环境配置
- 开发: `env/dev/application.yml`
- 测试: `env/test/application.yml`
- 生产: `env/prod/application.yml`

### 核心配置
```yaml
# MyBatis Plus
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  typeAliasesPackage: com.tianmasys.ebu.*.domain
  global-config:
    db-config:
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 安全配置
security:
  ignore:
    whites:
      - /auth/login
      - /ws/**
      - /iot/device/dealMqttEvent
```

## 性能优化

### 并发处理
- 使用`ConcurrentHashMap`存储共享数据
- 异步处理数据保存: `@Async`
- 并发读取Modbus数据

### 内存和数据库优化
- 定期清理过期数据
- 批量插入统计数据
- 合理使用索引和分页查询

## 开发要求

### 代码质量
- 单个文件不超过500行
- 遵循DRY原则
- 完善的异常处理和日志记录
- 使用`@Slf4j`进行日志记录

### 安全要求
- JWT Token认证
- 接口权限控制: `@RequiresPermissions`
- 敏感数据加密存储
- SQL注入和XSS防护

### 部署规范
```bash
# 打包
mvn clean package -Pdev  # 开发环境
mvn clean package -Pprod # 生产环境

# 启动
java -jar -Xms512m -Xmx1024m ebu-qingma-bim-server.jar
```

## 测试要求
- 核心业务逻辑单元测试
- Modbus连接集成测试
- WebSocket通信测试
- Mock外部依赖，测试异常场景

## 通用开发规则
- 始终用中文回复
- 严格遵循正确的代码格式、风格、规范和命名规范
- 文件行数控制在500行以内
- 遵循DRY原则
- 开发环境为Windows，注意生成正确的终端命令格式