package com.tianmasys.ebu.qingma.domain;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import java.util.Map;

/**
 * modbus点位数据记录表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Data
@TableName(value = "modbus_data_log", autoResultMap = true)
public class ModbusDataLogEntity extends BaseEntity {
	/**
	* 主键ID
	*/
	@TableId("id")
	private Long id;

	/**
	* 站点id
	*/
    @TableField("site_id")
	private Long siteId;

	/**
	* 从站id
	*/
    @TableField("slave_id")
	private Long slaveId;

	/**
	* 采集开始时间
	*/
    @TableField("start_time")
	private Date startTime;

	/**
	* 采集完成时间
	*/
    @TableField("fnish_time")
	private Date fnishTime;

	/**
	* 采集耗时（单位毫秒）
	*/
    @TableField("time")
	private Integer time;

	/**
	* 采集的数据
	*/
    @TableField(value = "data", typeHandler = JacksonTypeHandler.class)
	private Map<String, Object> data;

	/** 是否删除：0-正常；1-删除； */
	@TableField(fill = FieldFill.INSERT)
	@TableLogic
	private String deleteFlag;


}