package com.tianmasys.ebu.qingma.domain;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import java.util.Map;

/**
 * modbus统计数据统计表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-07
 */

@Data
@TableName(value = "modbus_data_statistics", autoResultMap = true)
public class ModbusDataStatisticsEntity extends BaseEntity {
	/**
	* 主键ID
	*/
	@TableId("id")
	private Long id;

	/**
	* 站点id
	*/
    @TableField("site_id")
	private Long siteId;

	/**
	* 统计类型：minute-分钟；hour-小时；day-天
	*/
    @TableField("stat_type")
	private String statType;

	/**
	* 统计开始时间
	*/
    @TableField("start_time")
	private Date startTime;

	/**
	* 统计结束时间
	*/
    @TableField("end_time")
	private Date endTime;

	/**
	* 统计后的数据
	*/
	@TableField(value = "data", typeHandler = JacksonTypeHandler.class)
	private Map<String, Object> data;

	/**
	* 是否删除：0-正常；1-删除；
	*/
	@TableField(fill = FieldFill.INSERT)
	@TableLogic
	private String deleteFlag;


}