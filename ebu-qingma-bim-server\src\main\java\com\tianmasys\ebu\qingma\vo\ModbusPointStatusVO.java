package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * Modbus点位状态查询VO
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "Modbus点位状态查询")
public class ModbusPointStatusVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "点位ID列表")
    private List<Long> pointIds;
}
